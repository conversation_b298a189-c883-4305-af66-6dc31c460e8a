<script lang="ts">
  import { getStores } from '$app/stores';
  const { page } = getStores();
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import {
    User,
    Shield,
    CreditCard,
    Users,
    Bell,
    FileText,
    Settings2,
    LogOut,
    Activity,
    Fingerprint,
    Bot,
    ChartColumnStacked,
    Share2,
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import * as Resizable from '$lib/components/ui/resizable';
  import { cn } from '$lib/utils.js';

  // Get user data from the page store
  $: userData = $page.data.user;
  $: hasTeamAccess = userData?.teamId || userData?.hasTeamFeature || false;

  // Navigation items array - base items always shown
  const baseNavItems = [
    { href: '/dashboard/settings', label: 'General', icon: Settings2, exact: true },
    { href: '/dashboard/settings/profile', label: 'Profile', icon: FileText },
    { href: '/dashboard/settings/usage', label: 'Usage', icon: Activity },
    { href: '/dashboard/settings/interview-coach', label: 'AI Coach', icon: Bot },
    { href: '/dashboard/settings/analysis', label: 'Analysis', icon: ChartColumnStacked },
    { href: '/dashboard/settings/account', label: 'Account', icon: User },
    { href: '/dashboard/settings/security', label: 'Security', icon: Shield },
    { href: '/dashboard/settings/billing', label: 'Billing', icon: CreditCard },
    { href: '/dashboard/settings/notifications', label: 'Notifications', icon: Bell },
    { href: '/dashboard/settings/referrals', label: 'Referrals', icon: Share2 },
  ];

  // Team navigation item - only shown if user has team access
  const teamNavItem = {
    href: '/dashboard/settings/team',
    label: 'Team',
    icon: Users,
    exact: false,
  };

  // Admin navigation items
  const adminNavItems = [
    { href: '/dashboard/settings/admin', label: 'Admin Settings', icon: Fingerprint, exact: false },
  ];

  // Computed navigation items based on user permissions
  $: navItems = hasTeamAccess ? [...baseNavItems, teamNavItem] : baseNavItems;

  // State
  let isAdmin = false;
  let isCollapsed = false;

  // Responsive settings
  const breakpoints = {
    desktop: 1024,
    tablet: 768,
  };

  // Default values based on viewport width
  const layoutSettings = {
    desktop: {
      defaultLayout: [20, 80],
      collapsedSize: 5,
      minSize: 12,
      maxSize: 14,
    },
    tablet: {
      defaultLayout: [25, 75],
      collapsedSize: 8,
      minSize: 16,
      maxSize: 22,
    },
  };

  // Current settings
  let currentSettings = layoutSettings.desktop;
  let defaultLayout = currentSettings.defaultLayout;

  // Handle resize
  function handleResize() {
    if (!browser) return;

    const width = window.innerWidth;

    if (width >= breakpoints.desktop) {
      currentSettings = layoutSettings.desktop;
    } else if (width >= breakpoints.tablet) {
      currentSettings = layoutSettings.tablet;
    }

    defaultLayout = currentSettings.defaultLayout;
  }

  // Set up resize listener
  onMount(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
  });

  onDestroy(() => {
    if (browser) {
      window.removeEventListener('resize', handleResize);
    }
  });

  // Check if user is admin on mount
  onMount(async () => {
    await checkAdminStatus();
  });

  // Check admin status
  async function checkAdminStatus() {
    try {
      const response = await fetch('/api/user/me');
      if (response.ok) {
        const userData = await response.json();
        isAdmin = userData.isAdmin === true;
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
    }
  }

  // Helper function to check if a path is active
  function isActive(path: string, exact = false): boolean {
    if (exact) {
      return $page.url.pathname === path;
    }
    return $page.url.pathname === path || $page.url.pathname.startsWith(path + '/');
  }

  // Logout function
  async function logout() {
    try {
      const res = await fetch('/api/auth/logout', { method: 'POST' });
      if (res.ok) {
        toast.success('Logged out successfully');
        window.location.href = '/auth/sign-in';
      } else {
        toast.error('Failed to log out', {
          description: 'Please try again later',
        });
      }
    } catch (error) {
      toast.error('Network error', {
        description: 'Could not connect to the server',
      });
      console.error('Logout error:', error);
    }
  }
</script>

<div class="flex h-[calc(100vh-65px)] w-full flex-col">
  <main class="flex flex-1 flex-col">
    <Resizable.PaneGroup
      direction="horizontal"
      onLayoutChange={(sizes: number[] | null) => sizes && (defaultLayout = sizes)}
      class="h-full items-stretch">
      <Resizable.Pane
        defaultSize={defaultLayout[0]}
        collapsedSize={currentSettings.collapsedSize}
        collapsible
        minSize={currentSettings.minSize}
        maxSize={currentSettings.maxSize}
        onCollapse={() => (isCollapsed = true)}
        onExpand={() => (isCollapsed = false)}>
        <div
          class={cn(
            'border-border flex h-full flex-col border-r',
            isCollapsed ? 'p-4 pt-6' : 'p-6'
          )}>
          <div class="flex flex-1 flex-col gap-4">
            {#each navItems as { href, label, icon, exact }}
              <a
                {href}
                class={cn(
                  'sm:text-md flex items-center text-sm font-light transition-colors xl:text-base',
                  isActive(href, exact)
                    ? 'text-foreground font-semibold'
                    : 'text-foreground/60 hover:text-foreground',
                  isCollapsed ? 'mb-4 justify-center' : 'gap-4'
                )}>
                {#if icon}
                  <svelte:component this={icon} class="h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4" />
                {/if}
                {#if !isCollapsed}
                  <span>{label}</span>
                {/if}
              </a>
            {/each}

            {#if isAdmin}
              <div class={cn('flex flex-col', isCollapsed ? 'mt-2' : 'mt-2 gap-4 border-t pt-4')}>
                {#each adminNavItems as { href, label, icon }}
                  <a
                    {href}
                    class={cn(
                      'sm:text-md flex items-center text-sm font-light transition-colors xl:text-base',
                      isActive(href)
                        ? 'text-foreground font-semibold'
                        : 'text-foreground/60 hover:text-foreground',
                      isCollapsed ? 'justify-center' : 'gap-4'
                    )}>
                    {#if icon}
                      <svelte:component this={icon} class="h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4" />
                    {/if}
                    {#if !isCollapsed}
                      <span>{label}</span>
                    {/if}
                  </a>
                {/each}
              </div>
            {/if}
          </div>

          <!-- Logout button at the bottom of the sidebar -->
          <div class={cn('mt-auto', isCollapsed ? 'flex justify-center' : 'border-t pt-4')}>
            <button
              on:click={logout}
              class={cn(
                'text-foreground/60 hover:text-foreground sm:text-md flex items-center text-sm font-light transition-colors xl:text-base',
                isCollapsed ? 'flex justify-center p-2' : ''
              )}>
              <div class={cn('flex items-center', isCollapsed ? '' : 'gap-4')}>
                <LogOut class="h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4" />
                {#if !isCollapsed}
                  <span>Log out</span>
                {/if}
              </div>
            </button>
          </div>
        </div>
      </Resizable.Pane>
      <Resizable.Handle withHandle />
      <Resizable.Pane defaultSize={defaultLayout[1]} minSize={45}>
        <div class="show-scrollbar w-auto overflow-auto">
          <slot />
        </div>
      </Resizable.Pane>
    </Resizable.PaneGroup>
  </main>
</div>
