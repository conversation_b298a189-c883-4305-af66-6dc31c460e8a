// cron/utils/improvedImprovedCircuitBreaker.ts
// Further improved circuit breaker implementation with enhanced memory handling and resilience

import { logger } from "./logger";
import os from "os";
import osu from "node-os-utils";
import { getContainerMetrics, isContainerized } from "./containerMetrics";
import { areJobsRunning, getRunningJobCount } from "./jobChecker";
import { spawn } from "child_process";

/**
 * Circuit breaker states
 */
export enum CircuitState {
  CLOSED = "CLOSED", // Normal operation, requests allowed
  OPEN = "OPEN", // Circuit is open, requests are blocked
  HALF_OPEN = "HALF_OPEN", // Testing if the system has recovered
  DEGRADED = "DEGRADED", // New state: System is under stress but still operational
}

/**
 * Circuit breaker options
 */
export interface CircuitBreakerOptions {
  // Thresholds for triggering the circuit breaker
  memoryThresholdPercent?: number; // Memory usage threshold (percentage)
  cpuThresholdPercent?: number; // CPU usage threshold (percentage)
  errorThresholdCount?: number; // Number of errors before opening circuit

  // Timing parameters
  resetTimeoutMs?: number; // Time to wait before attempting to close circuit (ms)
  checkIntervalMs?: number; // How often to check system resources (ms)

  // Consecutive readings required to change state
  consecutiveReadingsForOpen?: number; // Number of consecutive readings above threshold to open circuit
  consecutiveReadingsForClose?: number; // Number of consecutive readings below threshold to close circuit
  consecutiveReadingsForDegraded?: number; // Number of consecutive readings for degraded state

  // Degraded mode thresholds (lower than full open thresholds)
  degradedMemoryThresholdPercent?: number; // Memory threshold for degraded mode
  degradedCpuThresholdPercent?: number; // CPU threshold for degraded mode

  // Callbacks
  onStateChange?: (oldState: CircuitState, newState: CircuitState) => void;
}

/**
 * Further Improved Circuit Breaker implementation for system resources
 *
 * This class implements an enhanced circuit breaker pattern to prevent system overload.
 * It monitors system resources (CPU, memory) and error rates, and can
 * temporarily block operations when the system is under stress.
 *
 * Improvements over ImprovedCircuitBreaker:
 * - Adds a new DEGRADED state for graceful degradation
 * - More sophisticated resource monitoring
 * - Better handling of transient spikes
 * - Enhanced logging and diagnostics
 * - Memory leak detection
 */
export class ImprovedImprovedCircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private lastStateChange: Date = new Date();
  private errorCount: number = 0;
  private lastError: Date | null = null;
  private resetTimeout: NodeJS.Timeout | null = null;
  private checkInterval: NodeJS.Timeout | null = null;

  // Consecutive readings counters
  private consecutiveHighMemoryReadings: number = 0;
  private consecutiveHighCpuReadings: number = 0;
  private consecutiveLowResourceReadings: number = 0;
  private consecutiveDegradedReadings: number = 0;

  // Memory leak detection
  private memoryUsageHistory: number[] = [];
  private potentialMemoryLeak: boolean = false;

  // Configuration options with defaults
  private memoryThresholdPercent: number;
  private cpuThresholdPercent: number;
  private errorThresholdCount: number;
  private resetTimeoutMs: number;
  private checkIntervalMs: number;
  private consecutiveReadingsForOpen: number;
  private consecutiveReadingsForClose: number;
  private consecutiveReadingsForDegraded: number;
  private degradedMemoryThresholdPercent: number;
  private degradedCpuThresholdPercent: number;
  private onStateChange?: (
    oldState: CircuitState,
    newState: CircuitState
  ) => void;

  constructor(options: CircuitBreakerOptions = {}) {
    // Set default values or use provided options - adjusted for stability
    this.memoryThresholdPercent = options.memoryThresholdPercent ?? 90; // Increased to 90% to match actual system behavior
    this.cpuThresholdPercent = options.cpuThresholdPercent ?? 90; // Increased to 90% to match actual system behavior
    this.errorThresholdCount = options.errorThresholdCount ?? 3;
    this.resetTimeoutMs = options.resetTimeoutMs ?? 120000;
    this.checkIntervalMs = options.checkIntervalMs ?? 10000;
    this.consecutiveReadingsForOpen = options.consecutiveReadingsForOpen ?? 2;
    this.consecutiveReadingsForClose = options.consecutiveReadingsForClose ?? 5; // Increased from 3 to 5 for more stability
    this.consecutiveReadingsForDegraded =
      options.consecutiveReadingsForDegraded ?? 3; // Increased from 1 to 3 to prevent rapid oscillation

    // Degraded thresholds adjusted to prevent oscillation with typical memory usage (83-84%)
    this.degradedMemoryThresholdPercent =
      options.degradedMemoryThresholdPercent ?? 85; // Fixed value instead of percentage of threshold
    this.degradedCpuThresholdPercent =
      options.degradedCpuThresholdPercent ?? 85; // Fixed value instead of percentage of threshold

    this.onStateChange = options.onStateChange;

    // Start resource monitoring
    this.startMonitoring();

    logger.info(
      `🔄 ImprovedImprovedCircuitBreaker initialized with thresholds: ` +
        `Memory: ${this.memoryThresholdPercent}%, ` +
        `CPU: ${this.cpuThresholdPercent}%, ` +
        `Degraded Memory: ${this.degradedMemoryThresholdPercent}%, ` +
        `Degraded CPU: ${this.degradedCpuThresholdPercent}%`
    );
  }

  /**
   * Check if the circuit is closed (operations allowed)
   *
   * UPDATED: Now handles async resource checking
   */
  public async isClosed(): Promise<boolean> {
    // Check current state - allow operations if not OPEN
    return this.state !== CircuitState.OPEN;
  }

  /**
   * Get the current state of the circuit breaker
   */
  public getState(): CircuitState {
    return this.state;
  }

  /**
   * Record an error occurrence
   */
  public recordError(): void {
    this.errorCount++;
    this.lastError = new Date();
    logger.warn(
      `⚠️ Circuit breaker recorded error (count: ${this.errorCount}/${this.errorThresholdCount})`
    );

    // Check if we should open the circuit
    if (
      (this.state === CircuitState.CLOSED ||
        this.state === CircuitState.DEGRADED) &&
      this.errorCount >= this.errorThresholdCount
    ) {
      this.openCircuit("Too many errors");
    }
  }

  /**
   * Reset the error count
   */
  public resetErrors(): void {
    this.errorCount = 0;
    logger.info("✅ Circuit breaker error count reset");
  }

  /**
   * Manually open the circuit
   */
  public openCircuit(reason: string = "manual"): void {
    if (this.state !== CircuitState.OPEN) {
      const oldState = this.state;
      this.state = CircuitState.OPEN;
      this.lastStateChange = new Date();

      logger.warn(
        `🔴 Circuit breaker opened - operations will be blocked (reason: ${reason})`
      );

      // Notify state change
      if (this.onStateChange) {
        this.onStateChange(oldState, this.state);
      }

      // Schedule reset attempt
      this.scheduleReset();
    }
  }

  /**
   * Manually close the circuit
   *
   * UPDATED: Auto-resets circuit breaker when system resources return to normal
   * and restarts scheduled jobs
   */
  public closeCircuit(): void {
    if (this.state !== CircuitState.CLOSED) {
      const oldState = this.state;
      this.state = CircuitState.CLOSED;
      this.lastStateChange = new Date();
      this.errorCount = 0;

      // Reset consecutive readings counters
      this.consecutiveHighMemoryReadings = 0;
      this.consecutiveHighCpuReadings = 0;
      this.consecutiveLowResourceReadings = 0;
      this.consecutiveDegradedReadings = 0;

      logger.info("🟢 Circuit breaker closed - operations allowed");

      // Notify state change
      if (this.onStateChange) {
        this.onStateChange(oldState, this.state);
      }

      // Clear any pending reset
      if (this.resetTimeout) {
        clearTimeout(this.resetTimeout);
        this.resetTimeout = null;
      }

      // Log that the circuit breaker has been reset
      logger.info(
        "✅ Circuit breaker reset - system resources have returned to normal levels"
      );

      // Run the reset-circuit-breaker script to restart jobs
      try {
        logger.info("� Running reset-circuit-breaker script to restart jobs");

        // Use spawn to run the reset-circuit-breaker script
        const resetProcess = spawn("npm", ["run", "reset-circuit-breaker"], {
          cwd: process.cwd(),
          stdio: "pipe",
          detached: true,
        });

        // Log output from the reset process
        resetProcess.stdout?.on("data", (data) => {
          logger.info(`Reset script: ${data.toString().trim()}`);
        });

        resetProcess.stderr?.on("data", (data) => {
          logger.error(`Reset script error: ${data.toString().trim()}`);
        });

        resetProcess.on("close", (code) => {
          if (code === 0) {
            logger.info(
              "✅ Circuit breaker reset script completed successfully"
            );
          } else {
            logger.error(
              `❌ Circuit breaker reset script exited with code ${code}`
            );
          }
        });

        // Unref the child process so it doesn't keep the event loop running
        resetProcess.unref();
      } catch (error) {
        logger.error("❌ Error running reset-circuit-breaker script:", error);
      }
    }
  }

  /**
   * Enter degraded mode
   */
  private enterDegradedMode(reason: string): void {
    if (this.state === CircuitState.CLOSED) {
      const oldState = this.state;
      this.state = CircuitState.DEGRADED;
      this.lastStateChange = new Date();

      logger.warn(
        `🟠 Circuit breaker entering degraded mode (reason: ${reason})`
      );

      // Notify state change
      if (this.onStateChange) {
        this.onStateChange(oldState, this.state);
      }
    }
  }

  /**
   * Stop monitoring system resources
   */
  public stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      logger.info("🛑 Circuit breaker monitoring stopped");
    }

    if (this.resetTimeout) {
      clearTimeout(this.resetTimeout);
      this.resetTimeout = null;
    }
  }

  /**
   * Start monitoring system resources
   *
   * RE-ENABLED: Monitoring re-enabled to prevent server shutdown
   */
  private startMonitoring(): void {
    logger.info(
      "🔍 Circuit breaker monitoring ENABLED to prevent server shutdown"
    );

    // Start monitoring with a longer interval to reduce resource usage
    this.checkInterval = setInterval(async () => {
      await this.monitorResources();
    }, this.checkIntervalMs);

    logger.info(
      `🔄 Resource monitoring started with ${this.checkIntervalMs / 1000}s interval`
    );
  }

  /**
   * Schedule a reset attempt after the timeout period
   *
   * UPDATED: Now handles async reset
   */
  private scheduleReset(): void {
    // Clear any existing timeout
    if (this.resetTimeout) {
      clearTimeout(this.resetTimeout);
    }

    // Set a new timeout with async handling
    this.resetTimeout = setTimeout(async () => {
      try {
        await this.attemptReset();
      } catch (error) {
        logger.error(`Error during scheduled reset: ${error}`);
        // Reschedule another attempt
        this.scheduleReset();
      }
    }, this.resetTimeoutMs);

    logger.info(
      `⏳ Circuit breaker will attempt reset in ${this.resetTimeoutMs / 1000} seconds`
    );
  }

  /**
   * Attempt to reset the circuit (transition to half-open)
   *
   * UPDATED: Now handles async resource checking
   */
  private async attemptReset(): Promise<void> {
    if (this.state === CircuitState.OPEN) {
      try {
        // Check system resources before attempting reset
        const {
          memoryOk,
          cpuOk,
          degradedMemoryOk,
          degradedCpuOk,
          memoryUsage,
          cpuUsage,
        } = await this.checkSystemResources();

        // Force garbage collection if available before making a decision
        if (global.gc) {
          logger.info(
            `🧹 Running garbage collection before circuit reset attempt...`
          );
          global.gc();

          // Re-check resources after GC
          const postGC = await this.checkSystemResources();

          // Log the improvement
          logger.info(
            `🧠 Memory after GC: ${postGC.memoryUsage.toFixed(2)}% (was ${memoryUsage.toFixed(2)}%), CPU: ${postGC.cpuUsage.toFixed(2)}%`
          );

          // Use the post-GC values
          if (postGC.memoryOk && postGC.cpuOk) {
            const oldState = this.state;
            this.state = CircuitState.HALF_OPEN;
            this.lastStateChange = new Date();

            logger.info(
              "🟡 Circuit breaker half-open - testing system recovery after successful GC"
            );

            // Notify state change
            if (this.onStateChange) {
              this.onStateChange(oldState, this.state);
            }
            return;
          } else if (postGC.degradedMemoryOk && postGC.degradedCpuOk) {
            // If resources are below critical thresholds but above degraded thresholds,
            // transition to degraded mode instead of staying fully open
            const oldState = this.state;
            this.state = CircuitState.DEGRADED;
            this.lastStateChange = new Date();

            logger.info(
              "🟠 Circuit breaker entering degraded mode from open state after GC"
            );

            // Notify state change
            if (this.onStateChange) {
              this.onStateChange(oldState, this.state);
            }
            return;
          }
        }

        // If we didn't have GC or GC didn't help enough, proceed with normal logic
        if (memoryOk && cpuOk) {
          const oldState = this.state;
          this.state = CircuitState.HALF_OPEN;
          this.lastStateChange = new Date();

          logger.info("🟡 Circuit breaker half-open - testing system recovery");

          // Notify state change
          if (this.onStateChange) {
            this.onStateChange(oldState, this.state);
          }
        } else if (degradedMemoryOk && degradedCpuOk) {
          // If resources are below critical thresholds but above degraded thresholds,
          // transition to degraded mode instead of staying fully open
          const oldState = this.state;
          this.state = CircuitState.DEGRADED;
          this.lastStateChange = new Date();

          logger.info(
            "🟠 Circuit breaker entering degraded mode from open state"
          );

          // Notify state change
          if (this.onStateChange) {
            this.onStateChange(oldState, this.state);
          }
        } else {
          logger.warn(
            `⚠️ System resources still constrained (Memory: ${memoryUsage.toFixed(2)}%, CPU: ${cpuUsage.toFixed(2)}%), keeping circuit open`
          );

          // Schedule another reset attempt with a progressive backoff
          const currentResetTimeout = this.resetTimeoutMs;
          this.resetTimeoutMs = Math.min(this.resetTimeoutMs * 1.5, 300000); // Increase timeout by 50%, max 5 minutes
          this.scheduleReset();
          logger.info(
            `⏳ Increased reset timeout from ${currentResetTimeout / 1000}s to ${this.resetTimeoutMs / 1000}s due to continued resource constraints`
          );
        }
      } catch (error) {
        logger.error(`Error during circuit reset attempt: ${error}`);
        // Schedule another reset attempt
        this.scheduleReset();
      }
    }
  }

  /**
   * Monitor system resources and update circuit state accordingly
   *
   * UPDATED: Now handles async resource checking and job status
   */
  private async monitorResources(): Promise<void> {
    try {
      // Get system resources asynchronously
      const {
        memoryOk,
        cpuOk,
        degradedMemoryOk,
        degradedCpuOk,
        memoryUsage,
        cpuUsage,
      } = await this.checkSystemResources();

      // Update memory history for leak detection
      this.updateMemoryHistory(memoryUsage);

      // Update consecutive readings counters
      this.updateConsecutiveReadings(
        memoryOk,
        cpuOk,
        degradedMemoryOk,
        degradedCpuOk
      );

      // Log current resource usage
      this.logResourceUsage(memoryUsage, cpuUsage);

      // Check if any jobs are running with error handling
      let jobsRunning = false;
      let runningJobCount = 0;

      try {
        jobsRunning = await areJobsRunning();
      } catch (error) {
        logger.error(`Error checking if jobs are running: ${error}`);
        // Default to false if we can't check
        jobsRunning = false;
      }

      try {
        runningJobCount = await getRunningJobCount();
        // Ensure we have a valid number
        if (typeof runningJobCount !== "number" || isNaN(runningJobCount)) {
          logger.warn(
            `Invalid running job count: ${runningJobCount}, defaulting to 0`
          );
          runningJobCount = 0;
        }
      } catch (error) {
        logger.error(`Error getting running job count: ${error}`);
        // Default to 0 if we can't check
        runningJobCount = 0;
      }

      // If no jobs are running and we're not in CLOSED state, consider resetting
      if (!jobsRunning && this.state !== CircuitState.CLOSED) {
        logger.info(
          `🔄 No jobs running (count: ${runningJobCount}), considering circuit reset`
        );

        // If we're in OPEN state, try to reset immediately
        if (this.state === CircuitState.OPEN) {
          // Only reset if resources are below degraded thresholds
          if (degradedMemoryOk && degradedCpuOk) {
            logger.info(
              "✅ No jobs running and resources are good, resetting circuit breaker"
            );
            this.closeCircuit();
            return;
          } else {
            logger.info(
              "⚠️ No jobs running but resources still constrained, not resetting yet"
            );
          }
        }
        // If we're in HALF_OPEN or DEGRADED state, close if resources are good
        else if (memoryOk && cpuOk) {
          logger.info(
            "✅ No jobs running and resources are good, closing circuit breaker"
          );
          this.closeCircuit();
          return;
        }
      } else if (this.state !== CircuitState.CLOSED) {
        // Log job status if we're not in CLOSED state
        const jobStatusMessage = jobsRunning
          ? `${runningJobCount} jobs running`
          : "No jobs running";
        logger.info(`ℹ️ Job status: ${jobStatusMessage}`);
      }

      // Update circuit state based on resource usage and consecutive readings
      this.updateCircuitState(
        memoryOk,
        cpuOk,
        degradedMemoryOk,
        degradedCpuOk,
        memoryUsage,
        cpuUsage
      );
    } catch (error) {
      logger.error(`Error monitoring resources: ${error}`);
      // Don't change circuit state on error, just log it
    }
  }

  /**
   * Update memory history and check for potential memory leaks
   */
  private updateMemoryHistory(memoryUsage: number): void {
    // Keep last 15 readings (increased from 10)
    if (this.memoryUsageHistory.length >= 15) {
      this.memoryUsageHistory.shift();
    }
    this.memoryUsageHistory.push(memoryUsage);

    // Check for potential memory leak (consistently increasing memory usage)
    if (this.memoryUsageHistory.length >= 5) {
      // Check if memory usage is consistently increasing
      let increasing = true;
      for (let i = 1; i < this.memoryUsageHistory.length; i++) {
        if (this.memoryUsageHistory[i] <= this.memoryUsageHistory[i - 1]) {
          increasing = false;
          break;
        }
      }

      // Check for rapid memory growth (more than 5% in 3 consecutive readings)
      let rapidGrowth = false;
      if (this.memoryUsageHistory.length >= 3) {
        const recentReadings = this.memoryUsageHistory.slice(-3);
        const growthRates = [];

        for (let i = 1; i < recentReadings.length; i++) {
          const growthRate =
            ((recentReadings[i] - recentReadings[i - 1]) /
              recentReadings[i - 1]) *
            100;
          growthRates.push(growthRate);
        }

        rapidGrowth = growthRates.every((rate) => rate > 5); // 5% growth threshold
      }

      // If memory usage is consistently increasing or showing rapid growth, flag potential memory leak
      if ((increasing || rapidGrowth) && !this.potentialMemoryLeak) {
        this.potentialMemoryLeak = true;

        // Determine the severity of the leak
        const severity = rapidGrowth ? "CRITICAL" : "WARNING";

        logger.warn(
          `⚠️ Potential memory leak detected [${severity}] - memory usage ${rapidGrowth ? "rapidly increasing" : "consistently increasing"}`
        );

        // Try to force garbage collection if available
        if (global.gc) {
          logger.info(
            `🧹 Running garbage collection to mitigate potential memory leak...`
          );
          global.gc();

          // Check if GC helped
          setTimeout(() => {
            const currentMemory =
              ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
            const improvement = memoryUsage - currentMemory;

            if (improvement > 5) {
              logger.info(
                `✅ Garbage collection freed ${improvement.toFixed(2)}% memory`
              );
            } else {
              logger.warn(
                `⚠️ Garbage collection had minimal effect (${improvement.toFixed(2)}%), possible memory leak confirmed`
              );
            }
          }, 1000);
        }
      } else if (!increasing && !rapidGrowth && this.potentialMemoryLeak) {
        this.potentialMemoryLeak = false;
        logger.info(`✅ Memory usage pattern normal again`);
      }
    }
  }

  /**
   * Update consecutive readings counters
   *
   * IMPROVED: Better handling of degraded state detection
   */
  private updateConsecutiveReadings(
    memoryOk: boolean,
    cpuOk: boolean,
    degradedMemoryOk: boolean,
    degradedCpuOk: boolean
  ): void {
    // Update high memory readings
    if (!memoryOk) {
      this.consecutiveHighMemoryReadings++;
      this.consecutiveLowResourceReadings = 0;
      this.consecutiveDegradedReadings = 0; // Reset degraded counter when in critical state
    } else {
      this.consecutiveHighMemoryReadings = 0;
    }

    // Update high CPU readings
    if (!cpuOk) {
      this.consecutiveHighCpuReadings++;
      this.consecutiveLowResourceReadings = 0;
      this.consecutiveDegradedReadings = 0; // Reset degraded counter when in critical state
    } else {
      this.consecutiveHighCpuReadings = 0;
    }

    // Update degraded readings - only if we're not in critical state
    if (memoryOk && cpuOk) {
      if (!degradedMemoryOk || !degradedCpuOk) {
        this.consecutiveDegradedReadings++;
        // When in degraded state, we're not in "low resource" state
        this.consecutiveLowResourceReadings = 0;
      } else {
        // Resources are fully normal (below degraded thresholds)
        this.consecutiveDegradedReadings = 0;
        this.consecutiveLowResourceReadings++;
      }
    }
  }

  /**
   * Log current resource usage
   */
  private logResourceUsage(memoryUsage: number, cpuUsage: number): void {
    // Only log when state changes or resources are critically high
    const shouldLog =
      this.state !== CircuitState.CLOSED || memoryUsage > 80 || cpuUsage > 80;

    if (shouldLog) {
      logger.info(
        `🧠 Circuit breaker monitoring - Memory: ${memoryUsage.toFixed(2)}% ` +
          `(threshold: ${this.memoryThresholdPercent}%, degraded: ${this.degradedMemoryThresholdPercent}%), ` +
          `CPU: ${cpuUsage.toFixed(2)}% ` +
          `(threshold: ${this.cpuThresholdPercent}%, degraded: ${this.degradedCpuThresholdPercent}%), ` +
          `State: ${this.state}, ` +
          `Consecutive high memory: ${this.consecutiveHighMemoryReadings}/${this.consecutiveReadingsForOpen}, ` +
          `Consecutive high CPU: ${this.consecutiveHighCpuReadings}/${this.consecutiveReadingsForOpen}, ` +
          `Consecutive degraded: ${this.consecutiveDegradedReadings}/${this.consecutiveReadingsForDegraded}, ` +
          `Consecutive low: ${this.consecutiveLowResourceReadings}/${this.consecutiveReadingsForClose}`
      );
    }
  }

  /**
   * Update circuit state based on resource usage and consecutive readings
   *
   * IMPROVED: Added hysteresis to prevent rapid state changes
   */
  private updateCircuitState(
    _memoryOk: boolean, // Unused but kept for method signature consistency
    _cpuOk: boolean, // Unused but kept for method signature consistency
    _degradedMemoryOk: boolean, // Unused but kept for method signature consistency
    _degradedCpuOk: boolean, // Unused but kept for method signature consistency
    memoryUsage: number,
    cpuUsage: number
  ): void {
    // Get the time since the last state change
    const timeSinceLastChange = Date.now() - this.lastStateChange.getTime();
    const minStateChangeInterval = 60000; // Minimum 1 minute between state changes (hysteresis)

    // Critical resource usage - open circuit
    if (
      this.consecutiveHighMemoryReadings >= this.consecutiveReadingsForOpen ||
      this.consecutiveHighCpuReadings >= this.consecutiveReadingsForOpen
    ) {
      if (
        (this.state === CircuitState.CLOSED ||
          this.state === CircuitState.DEGRADED) &&
        timeSinceLastChange >= minStateChangeInterval
      ) {
        logger.warn(
          `⚠️ System resources consistently constrained - Memory: ${memoryUsage.toFixed(2)}%, CPU: ${cpuUsage.toFixed(2)}%`
        );
        this.openCircuit("High resource usage");
      } else if (this.state === CircuitState.HALF_OPEN) {
        logger.warn(
          "⚠️ System resources still consistently constrained, reverting to open circuit"
        );
        this.openCircuit("High resource usage during half-open test");
      }
    }
    // Degraded resource usage - enter degraded mode
    else if (
      this.state === CircuitState.CLOSED &&
      this.consecutiveDegradedReadings >= this.consecutiveReadingsForDegraded &&
      timeSinceLastChange >= minStateChangeInterval
    ) {
      // Only enter degraded mode if memory is consistently in the degraded range (not just spiking)
      // This helps prevent oscillation when memory is hovering around the threshold
      if (
        memoryUsage >= this.degradedMemoryThresholdPercent &&
        memoryUsage < this.memoryThresholdPercent - 2
      ) {
        // Add a 2% buffer
        this.enterDegradedMode("Approaching resource limits");
      }
    }
    // Resources back to normal - close circuit
    else if (
      (this.state === CircuitState.HALF_OPEN ||
        this.state === CircuitState.DEGRADED) &&
      this.consecutiveLowResourceReadings >= this.consecutiveReadingsForClose &&
      timeSinceLastChange >= minStateChangeInterval
    ) {
      // Only close the circuit if memory is well below the degraded threshold
      // This adds hysteresis to prevent oscillation
      if (memoryUsage < this.degradedMemoryThresholdPercent - 5) {
        // 5% buffer below degraded threshold
        logger.info("✅ System resources consistently normal, closing circuit");
        this.closeCircuit();
      } else {
        logger.info(
          `🔄 Memory (${memoryUsage.toFixed(2)}%) still close to degraded threshold (${this.degradedMemoryThresholdPercent}%), maintaining current state`
        );
      }
    }
  }

  /**
   * Check system resources and determine if they're within acceptable limits
   *
   * COMPLETELY REWRITTEN: Using container metrics when available, falling back to node-os-utils
   * - Prioritizes cgroups data for containerized environments (Docker, Render, etc.)
   * - Falls back to node-os-utils for non-containerized environments
   * - Further falls back to basic OS module calculations if all else fails
   */
  private async checkSystemResources(): Promise<{
    memoryOk: boolean;
    cpuOk: boolean;
    degradedMemoryOk: boolean;
    degradedCpuOk: boolean;
    memoryUsage: number;
    cpuUsage: number;
  }> {
    try {
      // First try to get container metrics (cgroups data)
      const containerMetrics = await getContainerMetrics();

      if (containerMetrics) {
        // We're in a containerized environment and could read cgroups data
        const memoryUsagePercent = containerMetrics.memoryUsagePercent;
        const cpuUsagePercent = containerMetrics.cpuUsagePercent;

        // Only log container metrics when state is not CLOSED or resources are high
        if (
          this.state !== CircuitState.CLOSED ||
          memoryUsagePercent > 70 ||
          cpuUsagePercent > 70
        ) {
          logger.debug(
            `📊 Container metrics from cgroups - Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%`
          );
        }

        return {
          memoryOk: memoryUsagePercent < this.memoryThresholdPercent,
          cpuOk: cpuUsagePercent < this.cpuThresholdPercent,
          degradedMemoryOk:
            memoryUsagePercent < this.degradedMemoryThresholdPercent,
          degradedCpuOk: cpuUsagePercent < this.degradedCpuThresholdPercent,
          memoryUsage: memoryUsagePercent,
          cpuUsage: cpuUsagePercent,
        };
      }

      // If we're in a container but couldn't get metrics, log a warning
      if (isContainerized()) {
        logger.warn(
          `⚠️ Running in container but couldn't read cgroups metrics, falling back to node-os-utils`
        );
      }

      // Fall back to node-os-utils
      const memInfo = await osu.mem.info();
      // memInfo.usedMemPercentage gives the actual used memory percentage
      const memoryUsagePercent = memInfo.usedMemPercentage;

      // Get CPU usage using node-os-utils
      // The 1000ms interval gives a good balance between accuracy and responsiveness
      const cpuUsagePercent = await osu.cpu.usage(1000);

      // Log the raw values for debugging
      logger.debug(
        `📊 System metrics from node-os-utils - Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%`
      );

      return {
        memoryOk: memoryUsagePercent < this.memoryThresholdPercent,
        cpuOk: cpuUsagePercent < this.cpuThresholdPercent,
        degradedMemoryOk:
          memoryUsagePercent < this.degradedMemoryThresholdPercent,
        degradedCpuOk: cpuUsagePercent < this.degradedCpuThresholdPercent,
        memoryUsage: memoryUsagePercent,
        cpuUsage: cpuUsagePercent,
      };
    } catch (error) {
      // If both container metrics and node-os-utils fail, fall back to basic OS module calculations
      logger.warn(
        `⚠️ All metric sources failed, falling back to basic OS metrics: ${error}`
      );

      // Memory fallback
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      const memoryUsagePercent = (usedMemory / totalMemory) * 100;

      // CPU fallback
      const cpuCount = os.cpus().length;
      const loadAvg = os.loadavg()[0]; // 1 minute load average
      const loadPerCpu = loadAvg / cpuCount;
      const cpuUsagePercent = Math.min(loadPerCpu * 100, 100); // Cap at 100%

      logger.debug(
        `📊 Fallback system metrics - Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%`
      );

      return {
        memoryOk: memoryUsagePercent < this.memoryThresholdPercent,
        cpuOk: cpuUsagePercent < this.cpuThresholdPercent,
        degradedMemoryOk:
          memoryUsagePercent < this.degradedMemoryThresholdPercent,
        degradedCpuOk: cpuUsagePercent < this.degradedCpuThresholdPercent,
        memoryUsage: memoryUsagePercent,
        cpuUsage: cpuUsagePercent,
      };
    }
  }
}
